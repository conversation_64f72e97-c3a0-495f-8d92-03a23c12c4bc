// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/AssetManager.h"
#include "Engine/DataTable.h"
#include "PreloadingGameMode.h"
#include "BIMAssetManager.generated.h"

USTRUCT(BlueprintType)
struct FGameResourceDependencies : public FTableRowBase
{
	GENERATED_BODY()

		UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString Name;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString Path;
};

/**
 * 
 */
UCLASS()
class QZS_API UBIMAssetManager : public UAssetManager
{
	GENERATED_BODY()

public:
	virtual void StartInitialLoading() override;

	UPROPERTY()
	TMap<FString, UStaticMesh*> PreloadObjects;

	// Stores paths of preloading resources
	TArray<FSoftObjectPath> ItemsToStream;

	// Callback for processing preloading resources done
	void SaveLoadedObjects();

	void StartPreload(APreloadingGameMode* GM);

private:
	APreloadingGameMode* GameMode;
	
};
