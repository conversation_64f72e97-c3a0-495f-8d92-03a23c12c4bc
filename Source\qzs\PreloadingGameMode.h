// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameMode.h"
#include "Engine/StreamableManager.h"
#include "Http.h"
#include "Json.h"
#include "PreloadingGameMode.generated.h"


DECLARE_DYNAMIC_MULTICAST_DELEGATE(FPreloadingStatusNotificationDelegate);

// 进度记录数据结构
USTRUCT(BlueprintType)
struct FQzsProgressRecord
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	FString CurrentDate;

	UPROPERTY(BlueprintReadWrite)
	FString ConstructCode;

	UPROPERTY(BlueprintReadWrite)
	FString TotalActualState;

	UPROPERTY(BlueprintReadWrite)
	FString TotalPlanState;

	UPROPERTY(BlueprintReadWrite)
	FString MonthPlanState;

	UPROPERTY(BlueprintReadWrite)
	FString YearPlanState;

	UPROPERTY(BlueprintReadWrite)
	FString DeviationState;
};

// 任务数据结构
USTRUCT(BlueprintType)
struct FTaskData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	FString ConstructCode;

	UPROPERTY(BlueprintReadWrite)
	FString ActualStartDate;

	UPROPERTY(BlueprintReadWrite)
	FString ActualEndDate;

	UPROPERTY(BlueprintReadWrite)
	FString PlanStartDate;

	UPROPERTY(BlueprintReadWrite)
	FString PlanEndDate;

	UPROPERTY(BlueprintReadWrite)
	FString MonthPlanStartDate;

	UPROPERTY(BlueprintReadWrite)
	FString MonthPlanEndDate;

	UPROPERTY(BlueprintReadWrite)
	FString YearPlanStartDate;

	UPROPERTY(BlueprintReadWrite)
	FString YearPlanEndDate;
};

/**
 *
 */
UCLASS()
class QZS_API APreloadingGameMode : public AGameMode
{
	GENERATED_BODY()

public:
	void NotifyPreloadingStatus() {
		if (OnPreloadingStatus.IsBound()) {
			OnPreloadingStatus.Broadcast();
		}
	}

	UPROPERTY(BlueprintReadWrite)
	FString LevelNameAfterPreloading = "NewWorld";

	UFUNCTION(BlueprintCallable)
	void StartPreload();
	UFUNCTION(BlueprintCallable)
	static void SetWindowMinimize();
	UFUNCTION(BlueprintCallable)
	void InitProgressDB();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	TArray<FQzsProgressRecord> GetProgressRecords() const { return ProgressRecords; }

	UFUNCTION(BlueprintCallable, BlueprintPure)
	int32 GetProgressRecordCount() const { return ProgressRecords.Num(); }
	UFUNCTION(BlueprintCallable)
	static void OpenFolder(FString path);
	UFUNCTION(BlueprintCallable)
	static void OpenBrowser(FString path);
	UFUNCTION(BlueprintCallable)
	TArray<FVector> GetRandomVerticesFromStaticMesh(UStaticMeshComponent* StaticMeshComponent, int32 NumVerticesToGet);
	UFUNCTION(BlueprintCallable)
	static int FindApp(FString path);
	
	// Multi-Dynamic delegate to notify Blueprint that preloading done
	UPROPERTY(BlueprintAssignable)
	FPreloadingStatusNotificationDelegate OnPreloadingStatus;
	


	UFUNCTION(BlueprintCallable)
	void ForceQuitGame();

	UFUNCTION(BlueprintCallable)
	void CheckAndKillCefHelperProcess();


	UFUNCTION(BlueprintCallable)
	void LoadLevelAsync();

	UFUNCTION(BlueprintPure)
	float GetLoadingProgress();

	FStreamableManager StreamableManager;
	TSharedPtr<FStreamableHandle> LoadingHandle;

private:
	// 状态计算函数
	FString CalcState(const FString& CurrentDate, const FString& StartDate, const FString& EndDate);
	FString CalcDeviationState(const FString& CurrentDate, const FString& ActualStartDate, const FString& ActualEndDate,
		const FString& PlanStartDate, const FString& PlanEndDate, const FString& MonthPlanStartDate,
		const FString& MonthPlanEndDate, const FString& YearPlanStartDate, const FString& YearPlanEndDate);

	// HTTP请求回调
	void OnHttpRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful);

	// 日期比较辅助函数
	bool IsDateBefore(const FString& Date1, const FString& Date2);

	// 进度记录存储
	TArray<FQzsProgressRecord> ProgressRecords;
};
