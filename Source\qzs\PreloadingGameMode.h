// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameMode.h"
#include "Engine/StreamableManager.h"
#include "PreloadingGameMode.generated.h"


DECLARE_DYNAMIC_MULTICAST_DELEGATE(FPreloadingStatusNotificationDelegate);

/**
 *
 */
UCLASS()
class QZS_API APreloadingGameMode : public AGameMode
{
	GENERATED_BODY()

public:
	void NotifyPreloadingStatus() {
		if (OnPreloadingStatus.IsBound()) {
			OnPreloadingStatus.Broadcast();
		}
	}

	UPROPERTY(BlueprintReadWrite)
	FString LevelNameAfterPreloading = "NewWorld";

	UFUNCTION(BlueprintCallable)
	void StartPreload();
	UFUNCTION(BlueprintCallable)
	static void SetWindowMinimize();
	UFUNCTION(BlueprintCallable)
	static void InitProgressDB();
	UFUNCTION(BlueprintCallable)
	static void OpenFolder(FString path);
	UFUNCTION(BlueprintCallable)
	static void OpenBrowser(FString path);
	UFUNCTION(BlueprintCallable)
	TArray<FVector> GetRandomVerticesFromStaticMesh(UStaticMeshComponent* StaticMeshComponent, int32 NumVerticesToGet);
	UFUNCTION(BlueprintCallable)
	static int FindApp(FString path);
	
	// Multi-Dynamic delegate to notify Blueprint that preloading done
	UPROPERTY(BlueprintAssignable)
	FPreloadingStatusNotificationDelegate OnPreloadingStatus;
	


	UFUNCTION(BlueprintCallable)
	void ForceQuitGame();

	UFUNCTION(BlueprintCallable)
	void CheckAndKillCefHelperProcess();


	UFUNCTION(BlueprintCallable)
	void LoadLevelAsync();

	UFUNCTION(BlueprintPure)
	float GetLoadingProgress();

	FStreamableManager StreamableManager;
	TSharedPtr<FStreamableHandle> LoadingHandle;
};
