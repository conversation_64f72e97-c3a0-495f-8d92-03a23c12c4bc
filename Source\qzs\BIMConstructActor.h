// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/StaticMeshActor.h"
#include "BIMConstructActor.generated.h"

/**
 * 
 */
UCLASS()
class QZS_API ABIMConstructActor : public AStaticMeshActor
{
	GENERATED_BODY()
	
public:
	UPROPERTY(BlueprintReadWrite, Category = BIM)
		TSoftObjectPtr<UStaticMesh> MeshPtr;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
		FString MeshPath;

	// ��������
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
		FString ConstructCode;

	// ����ƻ���ʼʱ��
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
		FDateTime PlanStartDate;

	// ����ƻ�����ʱ��
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
		FDateTime PlanEndDate;

	// ʵ�ʼƻ���ʼʱ��
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
		FDateTime ActualPlanStartDate;

	// ʵ�ʼƻ�����ʱ��
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
		FDateTime ActualPlanEndDate;

	// ʵ�ʿ�ʼʱ��
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
		FDateTime ActualStartDate;

	// ʵ�ʽ���ʱ��
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = BIM)
	FDateTime ActualEndDate;
};
