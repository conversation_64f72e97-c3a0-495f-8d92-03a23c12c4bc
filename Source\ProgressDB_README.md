# 进度数据库功能说明

## 概述
这个功能将Python代码转换为UE C++代码，实现了从HTTP API获取施工进度数据并进行状态计算的功能。

## 主要功能

### 1. 数据结构
- `FQzsProgressRecord`: 存储进度记录的结构体
- `FTaskData`: 存储任务数据的结构体

### 2. 核心函数
- `InitProgressDB()`: 初始化进度数据库，发送HTTP请求获取数据
- `CalcState()`: 计算基本状态（NotStarted/InProgress/Completed）
- `CalcDeviationState()`: 计算偏差状态（考虑超期情况）
- `IsDateBefore()`: 日期比较辅助函数

### 3. 新增的蓝图可调用函数
- `GetProgressRecords()`: 获取所有进度记录
- `GetProgressRecordCount()`: 获取进度记录数量

## 使用方法

### 在蓝图中使用
1. 调用 `InitProgressDB()` 开始数据获取和处理
2. 等待HTTP请求完成（异步处理）
3. 使用 `GetProgressRecordCount()` 检查是否有数据
4. 使用 `GetProgressRecords()` 获取所有记录

### 在C++中使用
```cpp
// 获取GameMode实例
APreloadingGameMode* GameMode = Cast<APreloadingGameMode>(GetWorld()->GetAuthGameMode());
if (GameMode)
{
    // 初始化进度数据库
    GameMode->InitProgressDB();
    
    // 获取记录数量
    int32 RecordCount = GameMode->GetProgressRecordCount();
    
    // 获取所有记录
    TArray<FQzsProgressRecord> Records = GameMode->GetProgressRecords();
}
```

## 状态说明

### 基本状态
- `NotStarted`: 未开始
- `InProgress`: 进行中  
- `Completed`: 已完成

### 偏差状态
- `NotStarted`: 未开始（正常）
- `NotStartedOverdue`: 未开始（超期）
- `InProgress`: 进行中（正常）
- `InProgressOverdue`: 进行中（超期）
- `Completed`: 已完成（正常）
- `CompletedOverdue`: 已完成（超期）

## 技术实现

### 依赖模块
在 `qzs.Build.cs` 中添加了以下模块：
- `HTTP`: HTTP请求功能
- `Json`: JSON解析功能
- `JsonUtilities`: JSON工具函数

### HTTP请求
- URL: `http://118.178.254.145:8889/api/construction_project/v2/projects/progress/tasks`
- 方法: GET
- 异步处理，使用回调函数处理响应

### 数据处理
1. 解析JSON响应获取 `dateRange` 和 `tasks` 数组
2. 遍历每个日期和每个任务
3. 计算各种状态
4. 存储到 `ProgressRecords` 数组中

## 注意事项

1. **异步处理**: HTTP请求是异步的，需要等待回调完成
2. **日期格式**: 假设日期格式为ISO8601标准格式
3. **错误处理**: 包含了HTTP请求失败和JSON解析失败的错误处理
4. **内存管理**: 使用UE的TArray自动管理内存
5. **线程安全**: HTTP回调在主线程执行，无需额外的线程同步

## 扩展建议

1. **数据持久化**: 可以添加SaveGame系统保存数据到本地
2. **SQLite集成**: 如需要真正的数据库功能，可以集成SQLite插件
3. **缓存机制**: 添加数据缓存避免重复请求
4. **进度通知**: 添加委托通知数据处理完成
