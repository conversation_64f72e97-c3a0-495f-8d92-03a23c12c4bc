// Fill out your copyright notice in the Description page of Project Settings.


#include "PreloadingGameMode.h"
#include "BIMAssetManager.h"
#include "Engine/GameEngine.h"
#include "EngineGlobals.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "StaticMeshResources.h"

#include <stdio.h>
#include <stdlib.h>
#include "HAL/PlatformProcess.h"
#include "HAL/FileManager.h"
#include "Misc/Paths.h"

#include "Windows/AllowWindowsPlatformTypes.h"
#include "Windows/PreWindowsApi.h"
#include <windows.h> //冲突头文件
#include <shellapi.h>
#include <tchar.h>
#include "Windows/PostWindowsApi.h"
#include "Windows/HideWindowsPlatformTypes.h"
#include <string>

#include "Engine/LevelStreamingDynamic.h"
#include "Kismet/GameplayStatics.h"
#include <tlhelp32.h>
using namespace std;


void APreloadingGameMode::ForceQuitGame()
{

	// 杀死 cefhelper.exe
	FPlatformProcess::ExecProcess(TEXT("taskkill"), TEXT("/f /im cefhelper.exe"), nullptr, nullptr, nullptr);
	FString ScriptPath = FPaths::ProjectDir() / TEXT("clean_saved.bat");
    FPlatformProcess::CreateProc(*ScriptPath, nullptr, true, false, false, nullptr, 0, nullptr, nullptr);
	// 获取本地AppData路径
	// FString LocalAppDataPath = FPlatformProcess::UserSettingsDir();

	// // 构建完整路径
	// FString SavedPath = FPaths::Combine(LocalAppDataPath, TEXT("qzs"), TEXT("Saved"));
	// UE_LOG(LogTemp, Warning, TEXT("SavedPath SavedPath: %s"), *SavedPath);

	// // 规范化路径
	// FPaths::NormalizeFilename(SavedPath);
	// UE_LOG(LogTemp, Warning, TEXT("SavedPath NormalizeFilename: %s"), *SavedPath);
	// // 检查目录是否存在
	// if (IFileManager::Get().DirectoryExists(*SavedPath))
	// {
	// 	// 删除目录及其所有内容
	// 	IFileManager::Get().DeleteDirectory(*SavedPath, true, true);

	// 	UE_LOG(LogTemp, Log, TEXT("SavedPath 已删除目录: %s"), *SavedPath);
	// }
	// else
	// {
	// 	UE_LOG(LogTemp, Warning, TEXT("SavedPath 目录不存在: %s"), *SavedPath);
	// }

	HANDLE hProcess = GetCurrentProcess();
	TerminateProcess(hProcess, 0);

	// 延迟2秒执行逻辑
	// FTimerHandle TimerHandle;
	// GetWorld()->GetTimerManager().SetTimer(TimerHandle, this, &APreloadingGameMode::CheckAndKillCefHelperProcess, 2.0f, false);
}


void APreloadingGameMode::CheckAndKillCefHelperProcess()
{
#if PLATFORM_WINDOWS
	// 查找cefhelper.exe进程
	HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hSnapshot == INVALID_HANDLE_VALUE)
	{
		return;
	}

	PROCESSENTRY32 pe;
	pe.dwSize = sizeof(PROCESSENTRY32);

	if (Process32First(hSnapshot, &pe))
	{
		do
		{
			if (_wcsicmp(pe.szExeFile, L"cefhelper.exe") == 0)
			{
				// 找到cefhelper.exe进程，杀死它
				HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0, pe.th32ProcessID);
				if (hProcess != NULL)
				{
					TerminateProcess(hProcess, 0);
					CloseHandle(hProcess);
				}
				break;
			}
		} while (Process32Next(hSnapshot, &pe));
	}

	CloseHandle(hSnapshot);
#endif
}

// 开始异步加载关卡
void APreloadingGameMode::LoadLevelAsync()
{
	// 定义需要加载的资源路径
	TArray<FSoftObjectPath> AssetPaths;
	AssetPaths.Add(FSoftObjectPath("/BIMModelDLC/Maps/Map_BGMODEL"));
	AssetPaths.Add(FSoftObjectPath("/BIMModelDLC/Maps/Map_JianCe"));
	AssetPaths.Add(FSoftObjectPath("/BIMModelDLC/Maps/Map_BaoZhaTu"));
	AssetPaths.Add(FSoftObjectPath("/Game/StarterContent/Maps/Map_BGMODEL_V3"));

	// 使用 FStreamableManager 异步加载关卡
	LoadingHandle = StreamableManager.RequestAsyncLoad(AssetPaths);
}

// 获取当前加载进度
float APreloadingGameMode::GetLoadingProgress()
{
	if (LoadingHandle.IsValid())
	{
		return LoadingHandle->GetProgress(); // 如果没有加载句柄，返回 1 表示已完成
	}
	return 1.0f; // 如果没有加载句柄，返回 1 表示已完成
}

void APreloadingGameMode::StartPreload()
{
	UBIMAssetManager* AssetManager = (UBIMAssetManager*)GEngine->AssetManager;

	AssetManager->StartPreload(this);
}

void APreloadingGameMode::SetWindowMinimize() {
	GEngine->GameViewport->GetWindow()->Minimize();
}
void APreloadingGameMode::InitProgressDB() {
	
}

void APreloadingGameMode::OpenFolder(FString path) {
	char path2[100];
	for (int i = 0; i <= path.Len() - 1; i++) {
		path2[i] = path[i];
	}
	// system("start \"\" \"G:\\new_312\\bim-constructmgmt-ue-new\"");
	system(path2);
}

void APreloadingGameMode::OpenBrowser(FString path) {
	try {
		UE_LOG(LogTemp, Error, TEXT("OpenBrowser Start"));
		string orig = TCHAR_TO_UTF8(*path);
		UE_LOG(LogTemp, Error, TEXT("string orig:%s"), *path);
		
		size_t origsize = orig.length() + 1;
		UE_LOG(LogTemp, Error, TEXT("origsize"));
		
		size_t convertedChars = 0;
		UE_LOG(LogTemp, Error, TEXT("convertedChars"));
		
		wchar_t* wcstring = (wchar_t*)malloc(sizeof(wchar_t) * (origsize));
		UE_LOG(LogTemp, Error, TEXT("wcstring"));
		
		if (wcstring != NULL) {
			mbstowcs_s(&convertedChars, wcstring, origsize, orig.c_str(), _TRUNCATE);
			UE_LOG(LogTemp, Error, TEXT("mbstowcs_s"));
			
			HINSTANCE result = ShellExecute(NULL, _T("open"), wcstring, NULL, NULL, SW_SHOW);
			INT_PTR resultCode = reinterpret_cast<INT_PTR>(result); // 使用 INT_PTR 替代 int
			if (resultCode <= 32) {
				UE_LOG(LogTemp, Error, TEXT("ShellExecute failed with error code: %lld"), resultCode);
			} else {
				UE_LOG(LogTemp, Error, TEXT("OpenBrowser End"));
			}
			
			free(wcstring); // 释放内存
		} else {
			UE_LOG(LogTemp, Error, TEXT("Failed to allocate memory for wcstring"));
		}
	} catch (const std::exception& e) {
		UE_LOG(LogTemp, Error, TEXT("Caught exception: %s"), *FString(e.what()));
	}
	// catch (exception e) {
	// 	// 处理异常
	// 	UE_LOG(LogTemp, Warning, TEXT("StaticMesh is null or does not have render data."));
	// 	UE_LOG(LogTemp, Error, TEXT("Caught exception: %s"), *FString(e.what()));
	// }
}

TArray<FVector> APreloadingGameMode::GetRandomVerticesFromStaticMesh(UStaticMeshComponent* StaticMeshComponent, int32 NumVerticesToGet)
{

	TArray<FVector3f> VectorPositions;
	TArray<FVector> WorldPositions;
	if (!StaticMeshComponent || !StaticMeshComponent->GetStaticMesh()->GetRenderData())
	{
		UE_LOG(LogTemp, Warning, TEXT("StaticMesh is null or does not have render data."));
		return WorldPositions;
	}

	FTransform WorldTransform = StaticMeshComponent->GetComponentTransform();

	// 获取LOD 0的数据
	FStaticMeshLODResources& LODResource = StaticMeshComponent->GetStaticMesh()->GetRenderData()->LODResources[0];

	// 获取顶点缓冲区
	FPositionVertexBuffer& VertexBuffer = LODResource.VertexBuffers.PositionVertexBuffer;

	// 顶点总数
	int32 TotalVertexCount = VertexBuffer.GetNumVertices();
	// NumVerticesToGet = TotalVertexCount; //强制赋值，使得结果返回所有顶点坐标

	UE_LOG(LogTemp, Log, TEXT("Total number of vertices: %d"), TotalVertexCount);

	if (NumVerticesToGet > TotalVertexCount)
	{
		UE_LOG(LogTemp, Warning, TEXT("Requested more vertices than are available in the mesh."));
		return WorldPositions;
	}
	// 随机选择N个顶点
	TArray<int32> SelectedIndices;
	while (SelectedIndices.Num() < NumVerticesToGet)
	{
		int32 RandomIndex = FMath::RandRange(0, TotalVertexCount - 1);
		if (!SelectedIndices.Contains(RandomIndex))
		{
			SelectedIndices.Add(RandomIndex);
			UE_LOG(LogTemp, Warning, TEXT("zzzRandomIndex"), RandomIndex);
			FVector3f VertexPosition = VertexBuffer.VertexPosition(RandomIndex);
			UE_LOG(LogTemp, Log, TEXT("Random Vertex %d: (%f, %f, %f)"), RandomIndex, VertexPosition.X, VertexPosition.Y, VertexPosition.Z);
			VectorPositions.Add(VertexPosition);
			FVector Vector(VertexPosition.X, VertexPosition.Y, VertexPosition.Z);
			FVector WorldPosition = WorldTransform.TransformPosition(Vector); // 将局部坐标转换为世界坐标
			WorldPositions.Add(WorldPosition);
		}
	}
	return WorldPositions;
}


int APreloadingGameMode::FindApp(FString path) {
	string orig = TCHAR_TO_UTF8(*path);
	// string orig = "http://************:18201/api/gx/QualityData2_detail/%E5%88%86%E9%A1%B9%E5%B7%A5%E7%A8%8B%E7%8E%B0%E5%9C%BA%E6%A3%80%E6%B5%8B%E8%AE%B0%E5%BD%95%E8%A1%A8.aip?token=RH-l1qIkG-V5mH9DtR6wZgd1g8dNug3rxdKfNyEP7C0&id=40894";
	size_t origsize = orig.length() + 1;
	const size_t newsize = 100;
	size_t convertedChars = 0;
	wchar_t* wcstring = (wchar_t*)malloc(sizeof(wchar_t) * (orig.length() - 1));
	mbstowcs_s(&convertedChars, wcstring, origsize, orig.c_str(), _TRUNCATE);
	FDateTime Time = FDateTime::Now();
	int64 Timestamp = Time.ToUnixTimestamp();
	UE_LOG(LogTemp, Warning, TEXT("IN StartPreload: %d"), Timestamp);
	HINSTANCE hNewExe = ShellExecute(NULL, _T("open"), wcstring, NULL, NULL, 0);
	//printf("return value:%d\n", orig);
	//SearchPath(NULL, L"winaip", L".exe", 100, NULL, NULL);
	return reinterpret_cast<intptr_t>(hNewExe);

}
