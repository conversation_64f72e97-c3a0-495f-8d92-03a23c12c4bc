// Fill out your copyright notice in the Description page of Project Settings.


#include "BIMAssetManager.h"

void UBIMAssetManager::StartInitialLoading()
{
	Super::StartInitialLoading();
}

void UBIMAssetManager::StartPreload(APreloadingGameMode* GM)
{
	uint32 Count = 0;

	FDateTime Time = FDateTime::Now();
	int64 Timestamp = Time.ToUnixTimestamp();
	UE_LOG(LogTemp, Warning, TEXT("IN StartPreload: %d"), Timestamp);

	if (GM) {
		GameMode = GM;
	}
	
	// Load preloading resource data table, which stores resources needed to be preloaded
	UDataTable* preloadResourceTable = LoadObject<UDataTable>(NULL, TEXT("DataTable'/Game/ConsScene/Datas/DT_Preloading_Asset_List2.DT_Preloading_Asset_List2'"));

	if (preloadResourceTable) {
		FStreamableManager& AssetLoader = UAssetManager::GetStreamableManager();
		FString ContextString;

		TArray< FGameResourceDependencies*> AllRows;
		preloadResourceTable->GetAllRows<FGameResourceDependencies>(ContextString, AllRows);

		FDateTime OneAssetLoadingStartTime;

		for (FGameResourceDependencies* row : AllRows) {
			OneAssetLoadingStartTime = FDateTime::Now();
			TCHAR* AssetPathName = row->Path.GetCharArray().GetData();

			// For game running in the Editor, we use sync loading
			// Otherwise we use async loading to speedup
			if (GIsEditor) {
				UStaticMesh* obj = (UStaticMesh*)StaticLoadObject(UStaticMesh::StaticClass(), NULL, AssetPathName, NULL, LOAD_None, NULL, false);

				if (obj) {
					PreloadObjects.Add(AssetPathName, obj);
				}

				UE_LOG(LogTemp, Warning, TEXT("Loading object:%s"), AssetPathName);
			}

			ItemsToStream.AddUnique(*(new FSoftObjectPath(AssetPathName)));

			Count++;
		}
	}

	if (GIsEditor && GM) {
		// Notify Gamemode preloading finishs
		UE_LOG(LogTemp, Warning, TEXT("Broadcast preloading status event"));
		GM->NotifyPreloadingStatus();
	}
	else if (!GIsEditor && GM) {
		UE_LOG(LogTemp, Warning, TEXT("Start to preloading..."), FDateTime::Now().ToUnixTimestamp());
		StreamableManager.RequestAsyncLoad(ItemsToStream, FStreamableDelegate::CreateUObject(this, &UBIMAssetManager::SaveLoadedObjects));
	}
	else {
		UE_LOG(LogTemp, Error, TEXT("GameMode is nullptr"));
	}

	UE_LOG(LogTemp, Warning, TEXT("EXIT StartPreload End: %d"), FDateTime::Now().ToUnixTimestamp());
}

// For async loading finish callback
void UBIMAssetManager::SaveLoadedObjects()
{
	UE_LOG(LogTemp, Warning, TEXT("Finish preloading all assets in BIMAssetManager!"));
	for (FSoftObjectPath SoftObj : ItemsToStream)
	{
		TAssetPtr<UStaticMesh> MeshAsset(SoftObj);

		UStaticMesh* MeshObj = MeshAsset.Get();
		if (MeshObj)
		{
			PreloadObjects.Add(SoftObj.GetLongPackageName(), MeshObj);
		}
	}

	if (GameMode) {
		GameMode->NotifyPreloadingStatus();
	}
	else {
		UE_LOG(LogTemp, Error, TEXT("Game mode in BIMAssetManager is nullptr"));
	}
}
